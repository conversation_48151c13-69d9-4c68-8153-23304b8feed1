import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:convert';
import '../models/document_model.dart';
import '../core/services/document_service.dart';
import '../core/services/firebase_service.dart';
import '../services/firebase_storage_sync_service.dart';
import '../services/file_category_management_service.dart';
import '../core/utils/anr_prevention.dart';

class DocumentProvider extends ChangeNotifier {
  List<DocumentModel> _documents = [];
  List<DocumentModel> _filteredDocuments = [];
  bool _isLoading = false;
  String? _errorMessage;
  String _searchQuery = '';
  String _selectedCategory = 'all';
  String _selectedStatus = 'all';
  String _selectedFileType = 'all';
  String _sortBy = 'uploadedAt';
  bool _sortAscending = false;

  // Dynamic document storage - persists during app session
  static final Map<String, List<DocumentModel>> _categoryDocuments = {};
  static bool _isInitialized = false;

  // Firebase real-time listener
  final FirebaseService _firebaseService = FirebaseService.instance;
  final DocumentService _documentService = DocumentService.instance;
  final FirebaseStorageSyncService _storageSyncService =
      FirebaseStorageSyncService.instance;
  StreamSubscription? _documentsSubscription;
  final bool _useFirebaseSync =
      true; // Enable Firebase sync for data persistence
  bool _isProcessingFirebaseUpdate = false; // Prevent duplicate processing
  Timer? _firebaseUpdateDebouncer; // Debounce Firebase updates

  // Getters
  List<DocumentModel> get documents => _filteredDocuments;
  List<DocumentModel> get allDocuments => _documents;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String get searchQuery => _searchQuery;
  String get selectedFileType => _selectedFileType;
  String get selectedCategory => _selectedCategory;
  String get selectedStatus => _selectedStatus;
  String get sortBy => _sortBy;
  bool get sortAscending => _sortAscending;

  // Load documents with Firebase real-time sync
  Future<void> loadDocuments() async {
    _setLoading(true);
    _clearError();

    try {
      // First try to sync Firebase Storage with Firestore and load documents
      bool firebaseDataLoaded = false;
      if (_useFirebaseSync) {
        try {
          debugPrint('🔄 Starting Firebase Storage sync...');

          // Perform comprehensive sync with timeout to prevent ANR
          final syncedDocuments = await ANRPrevention.executeNetworkOperation(
            _storageSyncService.syncStorageWithFirestore(),
            operationName: 'Firebase Storage Sync',
          );

          if (syncedDocuments != null && syncedDocuments.isNotEmpty) {
            debugPrint(
              ' Loading ${syncedDocuments.length} synced documents from Firebase',
            );
            _handleFirebaseDocumentModels(syncedDocuments);
            firebaseDataLoaded = true;
            _isInitialized = true;
            // Save synced data to local storage for offline access
            await _saveToStorage();
          } else {
            // Fallback to regular Firestore query if sync returns empty
            final firebaseDocuments =
                await ANRPrevention.executeNetworkOperation(
                  _documentService.getAllDocuments(),
                  operationName: 'Firestore Document Load',
                );
            if (firebaseDocuments != null && firebaseDocuments.isNotEmpty) {
              debugPrint(
                '📥 Loading ${firebaseDocuments.length} documents from Firebase service',
              );
              _handleFirebaseDocumentModels(firebaseDocuments);
              firebaseDataLoaded = true;
              _isInitialized = true;
              await _saveToStorage();
            }
          }
        } catch (firebaseError) {
          debugPrint('Firebase sync/load error: $firebaseError');
          // Continue to try local storage if Firebase fails
        }
      }

      // If Firebase data wasn't loaded, try local storage
      if (!firebaseDataLoaded) {
        await _loadFromStorage();

        // Only initialize sample data if no data exists anywhere
        if (!_isInitialized && _categoryDocuments.isEmpty) {
          debugPrint('📝 No data found, initializing sample data');
          _initializeSampleData();
          _isInitialized = true;
          await _saveToStorage();
        }
      }

      // Load all documents from dynamic storage
      _documents = [];
      _categoryDocuments.forEach((categoryId, docs) {
        _documents.addAll(docs);
      });

      // Start Firebase real-time listener if enabled
      if (_useFirebaseSync) {
        _startFirebaseListener();
      }

      _applyFiltersAndSort();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Start Firebase real-time listener for document updates
  void _startFirebaseListener() {
    try {
      _documentsSubscription?.cancel();
      _documentsSubscription = _firebaseService.documentsCollection
          .snapshots()
          .listen(
            (snapshot) {
              _handleFirebaseDocumentUpdates(snapshot.docs);
            },
            onError: (error) {
              debugPrint('Firebase listener error: $error');
              // Continue with local data if Firebase fails
            },
          );
    } catch (e) {
      debugPrint('Failed to start Firebase listener: $e');
      // Continue with local data if Firebase setup fails
    }
  }

  // Handle Firebase document updates from snapshots
  void _handleFirebaseDocumentUpdates(List<QueryDocumentSnapshot> docs) {
    // Debounce Firebase updates to prevent excessive calls - increased to 2 seconds
    _firebaseUpdateDebouncer?.cancel();
    _firebaseUpdateDebouncer = Timer(const Duration(seconds: 2), () {
      _processFirebaseDocumentUpdates(docs);
    });
  }

  // Process Firebase document updates (debounced)
  void _processFirebaseDocumentUpdates(List<QueryDocumentSnapshot> docs) {
    // Prevent duplicate processing
    if (_isProcessingFirebaseUpdate) {
      debugPrint('⚠️ Firebase update already in progress, skipping...');
      return;
    }

    try {
      _isProcessingFirebaseUpdate = true;
      // Only log if there are significant changes to reduce noise
      if (docs.length != _documents.length) {
        debugPrint(
          '📥 Processing ${docs.length} documents from Firebase listener',
        );
      }

      final firebaseDocuments = docs
          .map((doc) => DocumentModel.fromFirestore(doc as DocumentSnapshot))
          .toList();

      // Merge Firebase documents with local documents
      _mergeFirebaseDocuments(firebaseDocuments, isFromListener: true);

      // Apply filters and notify listeners
      _applyFiltersAndSort();
    } catch (e) {
      debugPrint('Error handling Firebase updates: $e');
    } finally {
      _isProcessingFirebaseUpdate = false;
    }
  }

  // Handle Firebase document updates from DocumentModel list (for direct service calls)
  void _handleFirebaseDocumentModels(List<DocumentModel> firebaseDocuments) {
    try {
      debugPrint(
        '📥 Loading ${firebaseDocuments.length} documents from Firebase service',
      );

      // Merge Firebase documents with local documents
      _mergeFirebaseDocuments(firebaseDocuments, isFromListener: false);

      // Apply filters and notify listeners
      _applyFiltersAndSort();
    } catch (e) {
      debugPrint('Error handling Firebase updates: $e');
    }
  }

  // Merge Firebase documents with local storage
  void _mergeFirebaseDocuments(
    List<DocumentModel> firebaseDocuments, {
    bool isFromListener = false,
  }) {
    // Only show merge log for significant changes to reduce noise
    if (isFromListener && firebaseDocuments.length != _documents.length) {
      debugPrint('🔄 Merging ${firebaseDocuments.length} Firebase documents');
    }

    bool hasChanges = false;

    for (final firebaseDoc in firebaseDocuments) {
      // Check if document already exists locally
      final existingIndex = _documents.indexWhere(
        (doc) => doc.id == firebaseDoc.id,
      );

      if (existingIndex != -1) {
        // Only update if there are actual changes
        final existingDoc = _documents[existingIndex];
        if (_hasDocumentChanged(existingDoc, firebaseDoc)) {
          _documents[existingIndex] = firebaseDoc;
          hasChanges = true;

          // Only log updates from listener to reduce noise
          if (isFromListener) {
            debugPrint(
              '🔄 Updated existing document: ${firebaseDoc.fileName} (ID: ${firebaseDoc.id})',
            );
          }

          // Update in category storage as well
          if (_categoryDocuments.containsKey(firebaseDoc.category)) {
            final categoryIndex = _categoryDocuments[firebaseDoc.category]!
                .indexWhere((doc) => doc.id == firebaseDoc.id);
            if (categoryIndex != -1) {
              _categoryDocuments[firebaseDoc.category]![categoryIndex] =
                  firebaseDoc;
            }
          }
        }
      } else {
        // Check if document already exists in category storage to prevent duplicates
        bool existsInCategory = false;
        if (_categoryDocuments.containsKey(firebaseDoc.category)) {
          existsInCategory = _categoryDocuments[firebaseDoc.category]!.any(
            (doc) => doc.id == firebaseDoc.id,
          );
        }

        if (!existsInCategory) {
          // Add new document
          _documents.add(firebaseDoc);
          hasChanges = true;

          debugPrint(
            '✅ Added new Firebase document: ${firebaseDoc.fileName} (ID: ${firebaseDoc.id})',
          );

          // Add to category storage
          if (!_categoryDocuments.containsKey(firebaseDoc.category)) {
            _categoryDocuments[firebaseDoc.category] = [];
          }
          _categoryDocuments[firebaseDoc.category]!.add(firebaseDoc);
        }
      }
    }

    // Only save if there were actual changes
    if (hasChanges) {
      _saveToStorage();
    }
  }

  // Check if document has changed (to avoid unnecessary updates)
  bool _hasDocumentChanged(DocumentModel existing, DocumentModel updated) {
    return existing.fileName != updated.fileName ||
        existing.fileSize != updated.fileSize ||
        existing.category != updated.category ||
        existing.status != updated.status ||
        existing.uploadedAt != updated.uploadedAt ||
        existing.metadata.description != updated.metadata.description;
  }

  // Initialize empty categories for production use
  void _initializeSampleData() {
    // No sample data in production - categories start empty
  }

  // Add document
  void addDocument(DocumentModel document) {
    // Check if document already exists to prevent duplicates
    if (_documents.any((doc) => doc.id == document.id)) {
      debugPrint(
        '⚠️ Document with ID ${document.id} already exists, skipping duplicate',
      );
      return;
    }

    // Add to category-specific storage
    if (!_categoryDocuments.containsKey(document.category)) {
      _categoryDocuments[document.category] = [];
    }

    // Check if document already exists in category to prevent duplicates
    if (_categoryDocuments[document.category]!.any(
      (doc) => doc.id == document.id,
    )) {
      debugPrint(
        '⚠️ Document with ID ${document.id} already exists in category ${document.category}, skipping duplicate',
      );
      return;
    }

    _categoryDocuments[document.category]!.insert(0, document);

    // Update main documents list
    _documents.insert(0, document);
    debugPrint(
      '✅ Document ${document.fileName} added successfully (ID: ${document.id})',
    );
    _applyFiltersAndSort();

    // Save to storage for persistence
    _saveToStorage();
  }

  // Add document to specific category (for uploads)
  void addDocumentToCategory(DocumentModel document, String categoryId) {
    final updatedDocument = document.copyWith(category: categoryId);

    // Check if document already exists to prevent duplicates
    if (_documents.any((doc) => doc.id == updatedDocument.id)) {
      debugPrint(
        '⚠️ Document with ID ${updatedDocument.id} already exists, skipping duplicate',
      );
      return;
    }

    // Add to category-specific storage
    if (!_categoryDocuments.containsKey(categoryId)) {
      _categoryDocuments[categoryId] = [];
    }

    // Check if document already exists in category to prevent duplicates
    if (_categoryDocuments[categoryId]!.any(
      (doc) => doc.id == updatedDocument.id,
    )) {
      debugPrint(
        '⚠️ Document with ID ${updatedDocument.id} already exists in category $categoryId, skipping duplicate',
      );
      return;
    }

    _categoryDocuments[categoryId]!.insert(0, updatedDocument);

    // Update main documents list
    _documents.insert(0, updatedDocument);
    debugPrint(
      '✅ Document ${updatedDocument.fileName} added to category $categoryId successfully (ID: ${updatedDocument.id})',
    );
    _applyFiltersAndSort();

    // Save to storage for persistence
    _saveToStorage();
  }

  // Batch update multiple documents to category (more efficient)
  Future<void> updateMultipleDocumentsCategory(
    List<String> documentIds,
    String categoryId,
  ) async {
    try {
      bool hasChanges = false;

      for (final documentId in documentIds) {
        final documentIndex = _documents.indexWhere(
          (doc) => doc.id == documentId,
        );
        if (documentIndex != -1) {
          final originalDocument = _documents[documentIndex];

          // Skip if already in the same category
          if (originalDocument.category == categoryId) {
            continue;
          }

          final updatedDocument = originalDocument.copyWith(
            category: categoryId,
          );

          // Update main documents list
          _documents[documentIndex] = updatedDocument;

          // Remove from old category storage
          if (_categoryDocuments.containsKey(originalDocument.category)) {
            _categoryDocuments[originalDocument.category]!.removeWhere(
              (doc) => doc.id == documentId,
            );
          }

          // Add to new category storage
          if (!_categoryDocuments.containsKey(categoryId)) {
            _categoryDocuments[categoryId] = [];
          }
          _categoryDocuments[categoryId]!.add(updatedDocument);

          hasChanges = true;
        }
      }

      // Only notify once after all updates
      if (hasChanges) {
        notifyListeners();
        // Save to storage
        await _saveToStorage();
      }
    } catch (e) {
      _setError('Failed to update documents category: $e');
      rethrow;
    }
  }

  // Update document category with Firebase Storage integration
  Future<void> updateDocumentCategory(
    String documentId,
    String categoryId,
  ) async {
    try {
      // Use the file category management service for proper file organization
      final fileCategoryService = FileCategoryManagementService();

      if (categoryId == 'uncategorized') {
        await fileCategoryService.moveFileToUncategorized(documentId);
      } else {
        await fileCategoryService.moveFileToCategory(documentId, categoryId);
      }

      final documentIndex = _documents.indexWhere(
        (doc) => doc.id == documentId,
      );
      if (documentIndex != -1) {
        final originalDocument = _documents[documentIndex];

        // Skip if already in the same category
        if (originalDocument.category == categoryId) {
          return;
        }

        final updatedDocument = originalDocument.copyWith(category: categoryId);

        // Update main documents list
        _documents[documentIndex] = updatedDocument;

        // Remove from old category storage
        if (_categoryDocuments.containsKey(originalDocument.category)) {
          _categoryDocuments[originalDocument.category]!.removeWhere(
            (doc) => doc.id == documentId,
          );
        }

        // Add to new category storage
        if (!_categoryDocuments.containsKey(categoryId)) {
          _categoryDocuments[categoryId] = [];
        }
        _categoryDocuments[categoryId]!.add(updatedDocument);

        // Only notify once at the end
        notifyListeners();

        // Save to storage
        await _saveToStorage();
      }
    } catch (e) {
      _setError('Failed to update document category: $e');
      rethrow;
    }
  }

  // Update document
  void updateDocument(DocumentModel document) {
    int index = _documents.indexWhere((d) => d.id == document.id);
    if (index != -1) {
      final oldDocument = _documents[index];
      _documents[index] = document;

      // Update category storage if category changed
      if (oldDocument.category != document.category) {
        // Remove from old category
        if (_categoryDocuments.containsKey(oldDocument.category)) {
          _categoryDocuments[oldDocument.category]!.removeWhere(
            (doc) => doc.id == document.id,
          );
        }

        // Add to new category
        if (!_categoryDocuments.containsKey(document.category)) {
          _categoryDocuments[document.category] = [];
        }
        _categoryDocuments[document.category]!.add(document);
      } else {
        // Update in same category
        if (_categoryDocuments.containsKey(document.category)) {
          final categoryIndex = _categoryDocuments[document.category]!
              .indexWhere((doc) => doc.id == document.id);
          if (categoryIndex != -1) {
            _categoryDocuments[document.category]![categoryIndex] = document;
          }
        }
      }

      _applyFiltersAndSort();
    }
  }

  // Remove document permanently (from Firebase Storage and Firestore)
  Future<void> removeDocument(String documentId, String deletedBy) async {
    try {
      // Delete from Firebase Storage and Firestore
      await _documentService.deleteDocument(documentId, deletedBy);

      // Find and remove from local category storage
      DocumentModel? docToRemove;
      String? categoryToRemoveFrom;

      for (final entry in _categoryDocuments.entries) {
        final doc = entry.value.firstWhere(
          (d) => d.id == documentId,
          orElse: () => DocumentModel(
            id: '',
            fileName: '',
            fileSize: 0,
            fileType: '',
            filePath: '',
            uploadedBy: '',
            uploadedAt: DateTime.now(),
            category: '',
            status: '',
            permissions: [],
            metadata: DocumentMetadata(description: '', tags: []),
          ),
        );
        if (doc.id.isNotEmpty) {
          docToRemove = doc;
          categoryToRemoveFrom = entry.key;
          break;
        }
      }

      if (docToRemove != null && categoryToRemoveFrom != null) {
        _categoryDocuments[categoryToRemoveFrom]!.removeWhere(
          (d) => d.id == documentId,
        );
      }

      // Remove from main list
      _documents.removeWhere((d) => d.id == documentId);
      _applyFiltersAndSort();

      // Save to storage for persistence
      await _saveToStorage();

      notifyListeners();
    } catch (e) {
      throw Exception('Failed to remove document: ${e.toString()}');
    }
  }

  // Search documents
  void searchDocuments(String query) {
    _searchQuery = query;
    _applyFiltersAndSort();
  }

  // Filter by category
  void filterByCategory(String category) {
    _selectedCategory = category;
    _applyFiltersAndSort();
  }

  // Filter by status
  void filterByStatus(String status) {
    _selectedStatus = status;
    _applyFiltersAndSort();
  }

  // Filter by file type
  void filterByFileType(String fileType) {
    _selectedFileType = fileType;
    _applyFiltersAndSort();
  }

  // Sort documents
  void sortDocuments(String sortBy, {bool ascending = false}) {
    _sortBy = sortBy;
    _sortAscending = ascending;
    _applyFiltersAndSort();
  }

  // Apply filters and sorting
  void _applyFiltersAndSort() {
    _filteredDocuments = _documents.where((document) {
      // Search filter
      bool matchesSearch =
          _searchQuery.isEmpty ||
          document.fileName.toLowerCase().contains(
            _searchQuery.toLowerCase(),
          ) ||
          document.metadata.description.toLowerCase().contains(
            _searchQuery.toLowerCase(),
          ) ||
          document.metadata.tags.any(
            (tag) => tag.toLowerCase().contains(_searchQuery.toLowerCase()),
          );

      // Category filter
      bool matchesCategory =
          _selectedCategory == 'all' || document.category == _selectedCategory;

      // Status filter
      bool matchesStatus =
          _selectedStatus == 'all' || document.status == _selectedStatus;

      // File type filter
      bool matchesFileType =
          _selectedFileType == 'all' ||
          _getFileTypeCategory(document.fileType) == _selectedFileType;

      return matchesSearch &&
          matchesCategory &&
          matchesStatus &&
          matchesFileType;
    }).toList();

    // Apply sorting
    _filteredDocuments.sort((a, b) {
      int comparison = 0;

      switch (_sortBy) {
        case 'fileName':
          comparison = a.fileName.compareTo(b.fileName);
          break;
        case 'fileSize':
          comparison = a.fileSize.compareTo(b.fileSize);
          break;
        case 'uploadedAt':
          comparison = a.uploadedAt.compareTo(b.uploadedAt);
          break;
        case 'category':
          comparison = a.category.compareTo(b.category);
          break;
        case 'status':
          comparison = a.status.compareTo(b.status);
          break;
        default:
          comparison = a.uploadedAt.compareTo(b.uploadedAt);
      }

      return _sortAscending ? comparison : -comparison;
    });

    notifyListeners();
  }

  // Clear filters
  void clearFilters() {
    _searchQuery = '';
    _selectedCategory = 'all';
    _selectedStatus = 'all';
    _selectedFileType = 'all';
    _sortBy = 'uploadedAt';
    _sortAscending = false;
    _applyFiltersAndSort();
  }

  // Get file type category for filtering
  String _getFileTypeCategory(String fileType) {
    final lowerFileType = fileType.toLowerCase();

    if (lowerFileType.contains('pdf')) {
      return 'PDF';
    } else if (lowerFileType.contains('doc') ||
        lowerFileType.contains('word')) {
      return 'DOC';
    } else if (lowerFileType.contains('excel') ||
        lowerFileType.contains('sheet') ||
        lowerFileType.contains('xlsx') ||
        lowerFileType.contains('xls')) {
      return 'Excel';
    } else if (lowerFileType.contains('image') ||
        lowerFileType.contains('jpg') ||
        lowerFileType.contains('jpeg') ||
        lowerFileType.contains('png')) {
      return 'Image';
    } else if (lowerFileType.contains('powerpoint') ||
        lowerFileType.contains('presentation') ||
        lowerFileType.contains('pptx') ||
        lowerFileType.contains('ppt')) {
      return 'PPT';
    } else if (lowerFileType.contains('text') ||
        lowerFileType.contains('txt')) {
      return 'TXT';
    } else {
      return 'Other';
    }
  }

  // Get document by ID
  DocumentModel? getDocumentById(String documentId) {
    try {
      return _documents.firstWhere((document) => document.id == documentId);
    } catch (e) {
      return null;
    }
  }

  // Get documents by category
  List<DocumentModel> getDocumentsByCategory(String category) {
    // Return documents from dynamic storage for this category
    return _categoryDocuments[category] ?? [];
  }

  // Get recent files (uploaded in the last 7 days, regardless of category)
  List<DocumentModel> getRecentFiles({int days = 7}) {
    final cutoffDate = DateTime.now().subtract(Duration(days: days));
    return _documents
        .where((doc) => doc.uploadedAt.isAfter(cutoffDate))
        .toList()
      ..sort((a, b) => b.uploadedAt.compareTo(a.uploadedAt));
  }

  // Get uncategorized files
  List<DocumentModel> getUncategorizedFiles() {
    return getDocumentsByCategory('uncategorized');
  }

  // Initialize empty category (for new categories)
  void initializeCategory(String categoryId) {
    if (!_categoryDocuments.containsKey(categoryId)) {
      _categoryDocuments[categoryId] = [];
    }
  }

  // Remove category and its documents
  void removeCategory(String categoryId) {
    _categoryDocuments.remove(categoryId);
    _documents.removeWhere((doc) => doc.category == categoryId);
    _applyFiltersAndSort();
  }

  // Get documents by status
  List<DocumentModel> getDocumentsByStatus(String status) {
    return _documents.where((document) => document.status == status).toList();
  }

  // Get documents by user
  List<DocumentModel> getDocumentsByUser(String userId) {
    return _documents
        .where((document) => document.uploadedBy == userId)
        .toList();
  }

  // Get recent documents
  List<DocumentModel> getRecentDocuments({int limit = 10}) {
    List<DocumentModel> sortedDocs = List.from(_documents);
    sortedDocs.sort((a, b) => b.uploadedAt.compareTo(a.uploadedAt));
    return sortedDocs.take(limit).toList();
  }

  // Get pending documents count
  int get pendingDocumentsCount {
    return _documents.where((document) => document.status == 'pending').length;
  }

  // Get approved documents count
  int get approvedDocumentsCount {
    return _documents.where((document) => document.status == 'approved').length;
  }

  // Get rejected documents count
  int get rejectedDocumentsCount {
    return _documents.where((document) => document.status == 'rejected').length;
  }

  // Get total documents count
  int get totalDocumentsCount {
    return _documents.length;
  }

  // Get total file size
  int get totalFileSize {
    return _documents.fold(0, (total, document) => total + document.fileSize);
  }

  // Get formatted total file size
  String get totalFileSizeFormatted {
    int totalSize = totalFileSize;
    if (totalSize < 1024) {
      return '$totalSize bytes';
    } else if (totalSize < 1024 * 1024) {
      return '${(totalSize / 1024).toStringAsFixed(1)} KB';
    } else if (totalSize < 1024 * 1024 * 1024) {
      return '${(totalSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(totalSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  // Refresh documents
  Future<void> refreshDocuments() async {
    await loadDocuments();
  }

  // Force refresh with Firebase Storage sync
  Future<void> refreshWithStorageSync() async {
    _setLoading(true);
    _clearError();

    try {
      debugPrint('🔄 Force refreshing with Firebase Storage sync...');

      // Perform comprehensive sync
      final syncedDocuments = await _storageSyncService
          .performComprehensiveSync();
      debugPrint('📊 Sync results: $syncedDocuments');

      // Reload documents after sync
      await loadDocuments();
    } catch (e) {
      debugPrint('❌ Force refresh with sync failed: $e');
      _setError('Failed to sync with Firebase Storage: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Force UI refresh (for immediate updates after uploads)
  void forceRefresh() {
    _applyFiltersAndSort();
    notifyListeners();
  }

  // Get sync status information
  Future<Map<String, dynamic>> getSyncStatus() async {
    try {
      return await _storageSyncService.getSyncStatus();
    } catch (e) {
      return {
        'error': e.toString(),
        'syncNeeded': true,
        'lastSyncCheck': DateTime.now().toIso8601String(),
      };
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Clear error manually
  void clearError() {
    _clearError();
  }

  @override
  void dispose() {
    // Cancel Firebase listener
    _documentsSubscription?.cancel();
    // Cancel debounce timer
    _firebaseUpdateDebouncer?.cancel();
    super.dispose();
  }

  // Save data to persistent storage
  Future<void> _saveToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Convert category documents to JSON
      final Map<String, dynamic> categoryData = {};
      _categoryDocuments.forEach((categoryId, docs) {
        categoryData[categoryId] = docs
            .map((doc) => doc.toMapForStorage())
            .toList();
      });

      await prefs.setString('category_documents', jsonEncode(categoryData));
      await prefs.setBool('documents_initialized', _isInitialized);
    } catch (e) {
      debugPrint('Error saving documents to storage: $e');
    }
  }

  // Load data from persistent storage
  Future<void> _loadFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load initialization status
      _isInitialized = prefs.getBool('documents_initialized') ?? false;

      // Load category documents
      final String? categoryDataString = prefs.getString('category_documents');
      if (categoryDataString != null) {
        final Map<String, dynamic> categoryData = jsonDecode(
          categoryDataString,
        );

        _categoryDocuments.clear();
        categoryData.forEach((categoryId, docsJson) {
          final List<DocumentModel> docs = (docsJson as List)
              .map((docJson) => DocumentModel.fromMapForStorage(docJson))
              .toList();
          _categoryDocuments[categoryId] = docs;
        });
      }
    } catch (e) {
      debugPrint('Error loading documents from storage: $e');
      // Reset to empty state if loading fails
      _categoryDocuments.clear();
      _isInitialized = false;
    }
  }
}
